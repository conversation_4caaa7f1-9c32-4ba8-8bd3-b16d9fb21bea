"""
示例配置文件
复制此文件为 config.py 并修改相应的值
"""

# API 配置示例
API_CONFIG = {
    # 基础 URL - 请替换为您的实际 funkyUrl
    "BASE_URL": "https://api.example.com/your-funky-url",
    
    # 请求头配置
    "USER_AGENT": "YourOperatorName/1.0",  # 请替换为您的操作员名称
    "AUTH_TOKEN": "your-auth-token-here",  # 请替换为您的实际认证令牌
    
    # 超时设置 (秒)
    "TIMEOUT": 30,
    
    # 重试配置
    "MAX_RETRIES": 3,
    "RETRY_DELAY": 1.0,
}

# 测试数据配置示例
TEST_CONFIG = {
    # 默认玩家 ID
    "DEFAULT_PLAYER_ID": "test_player_001",
    
    # 默认货币
    "DEFAULT_CURRENCY": "USD",
    
    # 默认游戏代码 - 请替换为您的实际游戏代码
    "DEFAULT_GAME_CODES": ["GAME001", "GAME002", "GAME003"],
    
    # 默认事件 ID - 请联系运营团队获取您的事件 ID
    "DEFAULT_EVENT_ID": "EVENT001",
    
    # 默认固定投注金额
    "DEFAULT_FIX_STAKE": 10.0,
    
    # 默认免费旋转次数
    "DEFAULT_FREE_SPIN": 5,
    
    # 优惠券有效期 (天)
    "VOUCHER_VALIDITY_DAYS": 30,
}

# 错误代码映射
ERROR_CODES = {
    0: "成功",
    400: "请求参数错误",
    601: "其他错误",
}

# 日志配置
LOG_CONFIG = {
    "LEVEL": "INFO",
    "FORMAT": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "FILE": "voucher_api.log",
}
