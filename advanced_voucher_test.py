#!/usr/bin/env python3
"""
高级 Voucher API 测试脚本
包含错误处理、日志记录和详细的测试用例
"""

import requests
import json
import uuid
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from config import API_CONFIG, TEST_CONFIG, ERROR_CODES, LOG_CONFIG


# 配置日志
logging.basicConfig(
    level=getattr(logging, LOG_CONFIG["LEVEL"]),
    format=LOG_CONFIG["FORMAT"],
    handlers=[
        logging.FileHandler(LOG_CONFIG["FILE"]),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class VoucherAPIError(Exception):
    """Voucher API 异常类"""
    def __init__(self, message: str, error_code: Optional[int] = None):
        super().__init__(message)
        self.error_code = error_code


class AdvancedVoucherAPIClient:
    """高级 Voucher API 客户端"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """初始化客户端"""
        self.config = config or API_CONFIG
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': self.config["USER_AGENT"],
            'Authentication': self.config["AUTH_TOKEN"],
            'Content-Type': 'application/json'
        })
        
        logger.info(f"初始化 VoucherAPIClient，基础 URL: {self.config['BASE_URL']}")
    
    def _generate_request_id(self) -> str:
        """生成唯一请求 ID"""
        return str(uuid.uuid4())
    
    def _make_request_with_retry(self, endpoint: str, data: Dict) -> Dict:
        """
        带重试机制的请求方法
        
        Args:
            endpoint: API 端点
            data: 请求数据
            
        Returns:
            API 响应
            
        Raises:
            VoucherAPIError: API 请求失败
        """
        url = f"{self.config['BASE_URL']}/Funky/User/{endpoint}"
        headers = {'X-Request-ID': self._generate_request_id()}
        
        logger.info(f"请求 {endpoint}，URL: {url}")
        logger.debug(f"请求数据: {json.dumps(data, indent=2)}")
        
        for attempt in range(self.config.get("MAX_RETRIES", 3)):
            try:
                response = self.session.post(
                    url, 
                    headers=headers, 
                    json=data, 
                    timeout=self.config.get("TIMEOUT", 30)
                )
                
                # 检查 HTTP 状态码
                response.raise_for_status()
                
                # 解析 JSON 响应
                response_data = response.json()
                
                # 检查业务错误码
                error_code = response_data.get("errorCode")
                if error_code != 0:
                    error_msg = response_data.get("errorMessage", "未知错误")
                    logger.error(f"API 业务错误: {error_code} - {error_msg}")
                    raise VoucherAPIError(f"API 错误: {error_msg}", error_code)
                
                logger.info(f"请求成功: {endpoint}")
                logger.debug(f"响应数据: {json.dumps(response_data, indent=2)}")
                
                return response_data
                
            except requests.exceptions.RequestException as e:
                logger.warning(f"请求失败 (尝试 {attempt + 1}/{self.config.get('MAX_RETRIES', 3)}): {e}")
                if attempt < self.config.get("MAX_RETRIES", 3) - 1:
                    time.sleep(self.config.get("RETRY_DELAY", 1.0))
                else:
                    raise VoucherAPIError(f"请求失败: {e}")
            
            except json.JSONDecodeError as e:
                logger.error(f"JSON 解析错误: {e}")
                raise VoucherAPIError(f"响应格式错误: {e}")
    
    def add_voucher(self, **kwargs) -> Dict:
        """添加单个优惠券"""
        required_fields = [
            'playerId', 'voucherId', 'fixStake', 'freeSpin',
            'effectiveDate', 'expiredDate', 'currency', 'gameCodes', 'eventId'
        ]
        
        # 验证必需字段
        for field in required_fields:
            if field not in kwargs:
                raise ValueError(f"缺少必需字段: {field}")
        
        return self._make_request_with_retry("AddVoucher", kwargs)
    
    def add_vouchers(self, vouchers_data: List[Dict]) -> Dict:
        """批量添加优惠券"""
        if not vouchers_data:
            raise ValueError("优惠券数据列表不能为空")
        
        data = {"data": vouchers_data}
        return self._make_request_with_retry("AddVouchers", data)
    
    def delete_voucher(self, voucher_id: str, player_id: Optional[str] = None) -> Dict:
        """删除优惠券"""
        if not voucher_id:
            raise ValueError("voucherId 不能为空")
        
        data = {"voucherId": voucher_id}
        if player_id:
            data["playerId"] = player_id
            
        return self._make_request_with_retry("DeleteVoucher", data)
    
    def get_vouchers(self, voucher_id_list: List[str]) -> Dict:
        """获取优惠券信息"""
        if not voucher_id_list:
            raise ValueError("优惠券 ID 列表不能为空")
        
        data = {"VoucherIdList": voucher_id_list}
        return self._make_request_with_retry("GetVouchers", data)


class VoucherTestSuite:
    """Voucher API 测试套件"""
    
    def __init__(self, client: AdvancedVoucherAPIClient):
        self.client = client
        self.test_results = []
    
    def create_test_voucher_data(self, player_id: str = None) -> Dict:
        """创建测试优惠券数据"""
        now = datetime.now()
        effective_date = now.isoformat() + "Z"
        expired_date = (now + timedelta(days=TEST_CONFIG["VOUCHER_VALIDITY_DAYS"])).isoformat() + "Z"
        
        return {
            "playerId": player_id or TEST_CONFIG["DEFAULT_PLAYER_ID"],
            "voucherId": f"test_voucher_{uuid.uuid4().hex[:8]}",
            "fixStake": TEST_CONFIG["DEFAULT_FIX_STAKE"],
            "freeSpin": TEST_CONFIG["DEFAULT_FREE_SPIN"],
            "effectiveDate": effective_date,
            "expiredDate": expired_date,
            "currency": TEST_CONFIG["DEFAULT_CURRENCY"],
            "gameCodes": TEST_CONFIG["DEFAULT_GAME_CODES"],
            "eventId": TEST_CONFIG["DEFAULT_EVENT_ID"]
        }
    
    def run_test(self, test_name: str, test_func, *args, **kwargs):
        """运行单个测试"""
        logger.info(f"开始测试: {test_name}")
        start_time = time.time()
        
        try:
            result = test_func(*args, **kwargs)
            duration = time.time() - start_time
            
            self.test_results.append({
                "name": test_name,
                "status": "PASS",
                "duration": duration,
                "result": result
            })
            
            logger.info(f"测试通过: {test_name} (耗时: {duration:.2f}s)")
            return result
            
        except Exception as e:
            duration = time.time() - start_time
            
            self.test_results.append({
                "name": test_name,
                "status": "FAIL",
                "duration": duration,
                "error": str(e)
            })
            
            logger.error(f"测试失败: {test_name} - {e} (耗时: {duration:.2f}s)")
            raise
    
    def test_add_single_voucher(self):
        """测试添加单个优惠券"""
        voucher_data = self.create_test_voucher_data()
        return self.client.add_voucher(**voucher_data)
    
    def test_add_multiple_vouchers(self):
        """测试批量添加优惠券"""
        vouchers = [
            self.create_test_voucher_data(f"player_{i}")
            for i in range(1, 4)
        ]
        return self.client.add_vouchers(vouchers)
    
    def test_get_vouchers(self, voucher_ids: List[str]):
        """测试获取优惠券"""
        return self.client.get_vouchers(voucher_ids)
    
    def test_delete_voucher(self, voucher_id: str, player_id: str):
        """测试删除优惠券"""
        return self.client.delete_voucher(voucher_id, player_id)
    
    def run_full_test_suite(self):
        """运行完整测试套件"""
        logger.info("开始运行完整测试套件")
        
        # 存储创建的优惠券 ID，用于后续测试
        created_voucher_ids = []
        
        try:
            # 测试 1: 添加单个优惠券
            result1 = self.run_test("添加单个优惠券", self.test_add_single_voucher)
            
            # 测试 2: 批量添加优惠券
            result2 = self.run_test("批量添加优惠券", self.test_add_multiple_vouchers)
            
            # 收集优惠券 ID
            # 注意：这里假设我们能从某种方式获取到创建的优惠券 ID
            # 实际情况可能需要根据 API 响应调整
            
            # 测试 3: 获取优惠券信息
            if created_voucher_ids:
                self.run_test("获取优惠券信息", self.test_get_vouchers, created_voucher_ids)
            
            # 测试 4: 删除优惠券
            if created_voucher_ids:
                for voucher_id in created_voucher_ids[:1]:  # 只删除第一个
                    self.run_test(
                        f"删除优惠券 {voucher_id}",
                        self.test_delete_voucher,
                        voucher_id,
                        TEST_CONFIG["DEFAULT_PLAYER_ID"]
                    )
            
        except Exception as e:
            logger.error(f"测试套件执行失败: {e}")
        
        finally:
            self.print_test_summary()
    
    def print_test_summary(self):
        """打印测试摘要"""
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if r["status"] == "PASS"])
        failed_tests = total_tests - passed_tests
        
        logger.info("=" * 50)
        logger.info("测试摘要")
        logger.info("=" * 50)
        logger.info(f"总测试数: {total_tests}")
        logger.info(f"通过: {passed_tests}")
        logger.info(f"失败: {failed_tests}")
        
        for result in self.test_results:
            status_symbol = "✓" if result["status"] == "PASS" else "✗"
            logger.info(f"{status_symbol} {result['name']} ({result['duration']:.2f}s)")
            
            if result["status"] == "FAIL":
                logger.info(f"  错误: {result.get('error', 'Unknown error')}")


def main():
    """主函数"""
    logger.info("启动 Voucher API 高级测试")
    
    try:
        # 创建客户端
        client = AdvancedVoucherAPIClient()
        
        # 创建测试套件
        test_suite = VoucherTestSuite(client)
        
        # 运行测试
        test_suite.run_full_test_suite()
        
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
