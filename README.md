# Voucher API Demo

这是一个用于测试 Funky Voucher API 的演示项目。

## 功能特性

- 支持所有 4 个 Voucher API 端点：
  - `AddVoucher` - 添加单个优惠券
  - `AddVouchers` - 批量添加优惠券
  - `DeleteVoucher` - 删除优惠券
  - `GetVouchers` - 获取优惠券信息

- 包含两个版本的测试脚本：
  - `voucher_api_demo.py` - 基础版本，简单易懂
  - `advanced_voucher_test.py` - 高级版本，包含错误处理、重试机制、日志记录

## 文件结构

```
voucher_demo/
├── README.md                    # 项目说明文档
├── requirements.txt             # Python 依赖包
├── config.py                    # 配置文件
├── voucher_api_demo.py         # 基础测试脚本
└── advanced_voucher_test.py    # 高级测试脚本
```

## 安装依赖

```bash
pip install -r requirements.txt
```

## 配置

在运行测试之前，需要修改 `config.py` 文件中的配置：

```python
API_CONFIG = {
    "BASE_URL": "https://your-actual-api-url.com/your-funky-url",  # 替换为实际的 API URL
    "USER_AGENT": "YourOperatorName/1.0",                         # 替换为您的操作员名称
    "AUTH_TOKEN": "your-actual-auth-token",                       # 替换为实际的认证令牌
}
```

## 使用方法

### 基础版本

```bash
python voucher_api_demo.py
```

### 高级版本

```bash
python advanced_voucher_test.py
```

## API 端点说明

### 1. AddVoucher - 添加单个优惠券

**请求参数：**
- `playerId` (string): 玩家唯一 ID，至少 3 个字符
- `voucherId` (string): 优惠券唯一 ID
- `fixStake` (number): 固定投注金额
- `freeSpin` (integer): 免费旋转次数
- `effectiveDate` (string): 生效时间 (ISO 8601 格式)
- `expiredDate` (string): 过期时间 (ISO 8601 格式)
- `currency` (string): 货币
- `gameCodes` (array): 游戏代码列表
- `eventId` (string): 事件 ID

### 2. AddVouchers - 批量添加优惠券

**请求参数：**
- `data` (array): 优惠券数据数组，每个元素包含与 AddVoucher 相同的字段

### 3. DeleteVoucher - 删除优惠券

**请求参数：**
- `voucherId` (string): 优惠券唯一 ID
- `playerId` (string, 可选): 玩家唯一 ID

### 4. GetVouchers - 获取优惠券信息

**请求参数：**
- `VoucherIdList` (array): 要查询的优惠券 ID 列表

## 错误代码

- `0`: 成功
- `400`: 请求参数错误
- `601`: 其他错误

## 注意事项

1. 所有 API 都需要在请求头中包含：
   - `User-Agent`: 操作员标识名称
   - `Authentication`: 认证令牌
   - `X-Request-ID`: 用于日志追踪的唯一标识符

2. 时间格式必须使用 ISO 8601 格式，例如：`2019-08-24T14:15:22Z`

3. 在使用优惠券功能之前，需要联系 FunkySupport 启用该功能

4. 测试脚本会生成随机的测试数据，包括优惠券 ID 和时间戳

## 日志

高级版本的测试脚本会生成日志文件 `voucher_api.log`，包含详细的请求和响应信息。

## 自定义测试

您可以通过修改 `config.py` 中的 `TEST_CONFIG` 来自定义测试数据：

```python
TEST_CONFIG = {
    "DEFAULT_PLAYER_ID": "your_test_player",
    "DEFAULT_CURRENCY": "EUR",
    "DEFAULT_GAME_CODES": ["YOUR_GAME_001", "YOUR_GAME_002"],
    "DEFAULT_EVENT_ID": "YOUR_EVENT_001",
    # ... 其他配置
}
```
