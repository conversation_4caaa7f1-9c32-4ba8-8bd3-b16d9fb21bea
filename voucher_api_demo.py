#!/usr/bin/env python3
"""
Voucher API Demo
测试 Funky Voucher API 的演示脚本
"""

import requests
import json
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional


class VoucherAPIClient:
    """Voucher API 客户端"""
    
    def __init__(self, base_url: str, user_agent: str, auth_token: str):
        """
        初始化客户端
        
        Args:
            base_url: API 基础 URL (funkyUrl)
            user_agent: 操作员标识名称
            auth_token: 认证令牌
        """
        self.base_url = base_url.rstrip('/')
        self.headers = {
            'User-Agent': user_agent,
            'Authentication': auth_token,
            'Content-Type': 'application/json'
        }
    
    def _generate_request_id(self) -> str:
        """生成请求 ID"""
        return str(uuid.uuid4())
    
    def _make_request(self, endpoint: str, data: Dict) -> Dict:
        """
        发送 API 请求
        
        Args:
            endpoint: API 端点
            data: 请求数据
            
        Returns:
            API 响应
        """
        url = f"{self.base_url}/Funky/User/{endpoint}"
        headers = self.headers.copy()
        headers['X-Request-ID'] = self._generate_request_id()
        
        print(f"\n=== 请求 {endpoint} ===")
        print(f"URL: {url}")
        print(f"Headers: {json.dumps(headers, indent=2)}")
        print(f"Data: {json.dumps(data, indent=2)}")
        
        try:
            response = requests.post(url, headers=headers, json=data, timeout=30)
            response_data = response.json()
            
            print(f"Status Code: {response.status_code}")
            print(f"Response: {json.dumps(response_data, indent=2)}")
            
            return response_data
        except requests.exceptions.RequestException as e:
            print(f"请求错误: {e}")
            return {"error": str(e)}
        except json.JSONDecodeError as e:
            print(f"JSON 解析错误: {e}")
            return {"error": f"JSON decode error: {e}"}
    
    def add_voucher(self, player_id: str, voucher_id: str, fix_stake: float,
                   free_spin: int, effective_date: str, expired_date: str,
                   currency: str, game_codes: List[str], event_id: str) -> Dict:
        """
        添加单个优惠券
        
        Args:
            player_id: 玩家唯一 ID
            voucher_id: 优惠券唯一 ID
            fix_stake: 固定投注金额
            free_spin: 免费旋转次数
            effective_date: 生效时间 (ISO 8601 格式)
            expired_date: 过期时间 (ISO 8601 格式)
            currency: 货币
            game_codes: 游戏代码列表
            event_id: 事件 ID
            
        Returns:
            API 响应
        """
        data = {
            "playerId": player_id,
            "voucherId": voucher_id,
            "fixStake": fix_stake,
            "freeSpin": free_spin,
            "effectiveDate": effective_date,
            "expiredDate": expired_date,
            "currency": currency,
            "gameCodes": game_codes,
            "eventId": event_id
        }
        
        return self._make_request("AddVoucher", data)
    
    def add_vouchers(self, vouchers_data: List[Dict]) -> Dict:
        """
        批量添加优惠券
        
        Args:
            vouchers_data: 优惠券数据列表
            
        Returns:
            API 响应
        """
        data = {
            "data": vouchers_data
        }
        
        return self._make_request("AddVouchers", data)
    
    def delete_voucher(self, voucher_id: str, player_id: Optional[str] = None) -> Dict:
        """
        删除优惠券
        
        Args:
            voucher_id: 优惠券唯一 ID
            player_id: 玩家唯一 ID (可选)
            
        Returns:
            API 响应
        """
        data = {
            "voucherId": voucher_id
        }
        
        if player_id:
            data["playerId"] = player_id
            
        return self._make_request("DeleteVoucher", data)
    
    def get_vouchers(self, voucher_id_list: List[str]) -> Dict:
        """
        获取优惠券信息
        
        Args:
            voucher_id_list: 优惠券 ID 列表
            
        Returns:
            API 响应
        """
        data = {
            "VoucherIdList": voucher_id_list
        }
        
        return self._make_request("GetVouchers", data)


def create_sample_voucher_data() -> Dict:
    """创建示例优惠券数据"""
    now = datetime.now()
    effective_date = now.isoformat() + "Z"
    expired_date = (now + timedelta(days=30)).isoformat() + "Z"
    
    return {
        "playerId": "player_001",
        "voucherId": f"voucher_{uuid.uuid4().hex[:8]}",
        "fixStake": 10.0,
        "freeSpin": 5,
        "effectiveDate": effective_date,
        "expiredDate": expired_date,
        "currency": "USD",
        "gameCodes": ["GAME001", "GAME002"],
        "eventId": "EVENT001"
    }


def demo_voucher_apis():
    """演示 Voucher API 的使用"""
    
    # 配置 API 客户端
    # 注意：这些是示例值，实际使用时需要替换为真实的值
    client = VoucherAPIClient(
        base_url="https://api.example.com/your-funky-url",
        user_agent="VoucherDemo/1.0",
        auth_token="your-auth-token-here"
    )
    
    print("=== Voucher API Demo 开始 ===")
    
    # 1. 测试添加单个优惠券
    print("\n1. 测试添加单个优惠券")
    voucher_data = create_sample_voucher_data()
    response1 = client.add_voucher(**voucher_data)
    
    # 2. 测试批量添加优惠券
    print("\n2. 测试批量添加优惠券")
    vouchers_list = [
        create_sample_voucher_data(),
        create_sample_voucher_data()
    ]
    response2 = client.add_vouchers(vouchers_list)
    
    # 3. 测试获取优惠券信息
    print("\n3. 测试获取优惠券信息")
    voucher_ids = [voucher_data["voucherId"]]
    if vouchers_list:
        voucher_ids.extend([v["voucherId"] for v in vouchers_list])
    response3 = client.get_vouchers(voucher_ids)
    
    # 4. 测试删除优惠券
    print("\n4. 测试删除优惠券")
    response4 = client.delete_voucher(
        voucher_id=voucher_data["voucherId"],
        player_id=voucher_data["playerId"]
    )
    
    print("\n=== Voucher API Demo 结束 ===")
    
    return {
        "add_voucher": response1,
        "add_vouchers": response2,
        "get_vouchers": response3,
        "delete_voucher": response4
    }


if __name__ == "__main__":
    # 运行演示
    results = demo_voucher_apis()
    
    print("\n=== 总结 ===")
    for api_name, result in results.items():
        error_code = result.get("errorCode", "N/A")
        print(f"{api_name}: errorCode = {error_code}")
